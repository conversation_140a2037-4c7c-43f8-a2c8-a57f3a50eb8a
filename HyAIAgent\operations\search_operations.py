"""
HyAIAgent 第四阶段 - 搜索操作模块
实现Tavily API集成和网络搜索功能
"""

import asyncio
import aiohttp
import json
import hashlib
import time
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import logging
from pathlib import Path

# 导入现有模块
from core.config_manager import ConfigManager
from operations.security_manager import SecurityManager
from core.kv_store import KVStore

# 配置日志
logger = logging.getLogger(__name__)


@dataclass
class SearchResult:
    """搜索结果数据模型"""
    title: str
    url: str
    content: str
    score: float
    published_date: Optional[str] = None
    source: str = "tavily"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)


@dataclass
class SearchResponse:
    """搜索响应数据模型"""
    query: str
    results: List[SearchResult]
    total_results: int
    search_time: float
    timestamp: datetime
    search_depth: str = "basic"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


class TavilySearchClient:
    """Tavily搜索API客户端"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.tavily.com"):
        """
        初始化Tavily搜索客户端
        
        Args:
            api_key: Tavily API密钥
            base_url: API基础URL
        """
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.session: Optional[aiohttp.ClientSession] = None
        self.request_count = 0
        self.last_request_time = 0
        self.rate_limit_delay = 1.0  # 请求间隔限制（秒）
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
            
    async def _ensure_session(self):
        """确保会话存在"""
        if not self.session:
            self.session = aiohttp.ClientSession()
            
    async def _rate_limit(self):
        """实施速率限制"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.rate_limit_delay:
            await asyncio.sleep(self.rate_limit_delay - time_since_last)
        self.last_request_time = time.time()
        
    async def search(self, 
                    query: str, 
                    search_depth: str = "basic",
                    max_results: int = 5,
                    include_domains: Optional[List[str]] = None,
                    exclude_domains: Optional[List[str]] = None,
                    timeout: int = 30) -> SearchResponse:
        """
        执行搜索请求
        
        Args:
            query: 搜索查询
            search_depth: 搜索深度 ("basic" 或 "advanced")
            max_results: 最大结果数量
            include_domains: 包含的域名列表
            exclude_domains: 排除的域名列表
            timeout: 请求超时时间
            
        Returns:
            SearchResponse: 搜索响应对象
            
        Raises:
            Exception: API请求失败时抛出异常
        """
        await self._ensure_session()
        await self._rate_limit()
        
        # 构建请求参数
        params = {
            "api_key": self.api_key,
            "query": query,
            "search_depth": search_depth,
            "max_results": max_results
        }
        
        if include_domains:
            params["include_domains"] = include_domains
        if exclude_domains:
            params["exclude_domains"] = exclude_domains
            
        start_time = time.time()
        
        try:
            # 发送API请求
            async with self.session.post(
                f"{self.base_url}/search",
                json=params,
                timeout=aiohttp.ClientTimeout(total=timeout)
            ) as response:
                
                self.request_count += 1
                
                if response.status == 200:
                    data = await response.json()
                    search_time = time.time() - start_time
                    
                    # 解析搜索结果
                    results = []
                    for item in data.get("results", []):
                        result = SearchResult(
                            title=item.get("title", ""),
                            url=item.get("url", ""),
                            content=item.get("content", ""),
                            score=item.get("score", 0.0),
                            published_date=item.get("published_date"),
                            source="tavily"
                        )
                        results.append(result)
                    
                    return SearchResponse(
                        query=query,
                        results=results,
                        total_results=len(results),
                        search_time=search_time,
                        timestamp=datetime.now(),
                        search_depth=search_depth
                    )
                else:
                    error_text = await response.text()
                    raise Exception(f"Tavily API请求失败: {response.status} - {error_text}")
                    
        except asyncio.TimeoutError:
            raise Exception(f"Tavily API请求超时 (>{timeout}秒)")
        except Exception as e:
            logger.error(f"Tavily搜索请求失败: {str(e)}")
            raise
            
    async def close(self):
        """关闭客户端会话"""
        if self.session:
            await self.session.close()
            self.session = None


class SearchOperations:
    """网络搜索操作管理器"""
    
    def __init__(self, 
                 config_manager: ConfigManager,
                 security_manager: Optional[SecurityManager] = None,
                 kv_store: Optional[KVStore] = None):
        """
        初始化搜索操作管理器
        
        Args:
            config_manager: 配置管理器
            security_manager: 安全管理器
            kv_store: 键值存储
        """
        self.config_manager = config_manager
        self.security_manager = security_manager
        self.kv_store = kv_store
        
        # 获取搜索配置
        self.search_config = self.config_manager.get("search", {})
        self.tavily_config = self.search_config.get("tavily", {})
        
        # 初始化Tavily客户端
        api_key = self.tavily_config.get("api_key", "")
        if not api_key or api_key == "${TAVILY_API_KEY}":
            raise ValueError("Tavily API密钥未配置，请在配置文件中设置search.tavily.api_key")
            
        self.tavily_client = TavilySearchClient(
            api_key=api_key,
            base_url=self.tavily_config.get("base_url", "https://api.tavily.com")
        )
        
        # 搜索统计
        self.search_stats = {
            "total_searches": 0,
            "successful_searches": 0,
            "failed_searches": 0,
            "cache_hits": 0,
            "total_search_time": 0.0
        }
        
    def _generate_cache_key(self, query: str, **kwargs) -> str:
        """生成缓存键"""
        cache_data = {"query": query, **kwargs}
        cache_str = json.dumps(cache_data, sort_keys=True)
        return f"search_cache_{hashlib.md5(cache_str.encode()).hexdigest()}"
        
    async def _get_cached_result(self, cache_key: str) -> Optional[SearchResponse]:
        """获取缓存的搜索结果"""
        if not self.kv_store:
            return None

        try:
            cached_data = self.kv_store.get(cache_key)
            if cached_data:
                # 检查缓存是否过期
                cache_ttl = self.search_config.get("cache", {}).get("ttl", 3600)
                cached_time = datetime.fromisoformat(cached_data["timestamp"])
                if datetime.now() - cached_time < timedelta(seconds=cache_ttl):
                    # 重构SearchResponse对象
                    results = [SearchResult(**result) for result in cached_data["results"]]
                    response = SearchResponse(
                        query=cached_data["query"],
                        results=results,
                        total_results=cached_data["total_results"],
                        search_time=cached_data["search_time"],
                        timestamp=cached_time,
                        search_depth=cached_data.get("search_depth", "basic")
                    )
                    self.search_stats["cache_hits"] += 1
                    return response
        except Exception as e:
            logger.warning(f"获取搜索缓存失败: {str(e)}")

        return None
        
    async def _cache_result(self, cache_key: str, response: SearchResponse):
        """缓存搜索结果"""
        if not self.kv_store:
            return

        try:
            cache_enabled = self.search_config.get("cache", {}).get("enabled", True)
            if cache_enabled:
                self.kv_store.set(cache_key, response.to_dict())
        except Exception as e:
            logger.warning(f"缓存搜索结果失败: {str(e)}")
            
    async def search_web(self, 
                        query: str, 
                        search_depth: str = "basic",
                        max_results: int = 5,
                        use_cache: bool = True,
                        **kwargs) -> SearchResponse:
        """
        执行网络搜索
        
        Args:
            query: 搜索查询
            search_depth: 搜索深度
            max_results: 最大结果数量
            use_cache: 是否使用缓存
            **kwargs: 其他搜索参数
            
        Returns:
            SearchResponse: 搜索响应
        """
        self.search_stats["total_searches"] += 1
        
        # 生成缓存键
        cache_key = self._generate_cache_key(query, search_depth=search_depth, max_results=max_results, **kwargs)
        
        # 尝试获取缓存结果
        if use_cache:
            cached_result = await self._get_cached_result(cache_key)
            if cached_result:
                logger.info(f"使用缓存搜索结果: {query}")
                return cached_result
        
        try:
            # 执行实际搜索
            logger.info(f"执行网络搜索: {query}")
            response = await self.tavily_client.search(
                query=query,
                search_depth=search_depth,
                max_results=max_results,
                timeout=self.tavily_config.get("timeout", 30),
                **kwargs
            )
            
            # 缓存结果
            if use_cache:
                await self._cache_result(cache_key, response)
            
            # 更新统计
            self.search_stats["successful_searches"] += 1
            self.search_stats["total_search_time"] += response.search_time
            
            logger.info(f"搜索完成: {query}, 结果数量: {response.total_results}, 用时: {response.search_time:.2f}秒")
            return response
            
        except Exception as e:
            self.search_stats["failed_searches"] += 1
            logger.error(f"搜索失败: {query} - {str(e)}")
            raise
            
    async def get_search_stats(self) -> Dict[str, Any]:
        """获取搜索统计信息"""
        stats = self.search_stats.copy()
        if stats["successful_searches"] > 0:
            stats["average_search_time"] = stats["total_search_time"] / stats["successful_searches"]
        else:
            stats["average_search_time"] = 0.0
            
        if stats["total_searches"] > 0:
            stats["success_rate"] = stats["successful_searches"] / stats["total_searches"]
            stats["cache_hit_rate"] = stats["cache_hits"] / stats["total_searches"]
        else:
            stats["success_rate"] = 0.0
            stats["cache_hit_rate"] = 0.0
            
        return stats
        
    async def close(self):
        """关闭搜索操作管理器"""
        await self.tavily_client.close()
