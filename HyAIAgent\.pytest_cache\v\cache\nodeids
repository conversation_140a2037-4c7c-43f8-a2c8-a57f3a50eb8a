["test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_advanced_search_basic", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_advanced_search_regex", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_advanced_search_with_content", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_advanced_search_with_filters", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_batch_checksum_calculation", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_batch_content_processing", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_batch_copy_files", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_batch_delete_files", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_batch_move_files", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_batch_rename_files", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_file_filter", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_processing_stats", "test/test_config_processor.py::TestConfigProcessor::test_basic_config_processing", "test/test_config_processor.py::TestConfigProcessor::test_config_backup", "test/test_config_processor.py::TestConfigProcessor::test_config_format_conversion", "test/test_config_processor.py::TestConfigProcessor::test_config_merge", "test/test_config_processor.py::TestConfigProcessor::test_config_validation", "test/test_config_processor.py::TestConfigProcessor::test_config_validation_standalone", "test/test_config_processor.py::TestConfigProcessor::test_environment_variable_replacement", "test/test_config_processor.py::TestConfigProcessor::test_error_handling", "test/test_config_processor.py::TestConfigProcessor::test_format_detection", "test/test_config_processor.py::TestConfigProcessor::test_ini_config_processing", "test/test_config_processor.py::TestConfigProcessor::test_nested_key_operations", "test/test_config_processor.py::TestConfigProcessor::test_statistics_tracking", "test/test_config_processor.py::TestConfigProcessor::test_yaml_config_processing", "test/test_directory_operations.py::TestDirectoryOperations::test_copy_directory_overwrite", "test/test_directory_operations.py::TestDirectoryOperations::test_copy_directory_success", "test/test_directory_operations.py::TestDirectoryOperations::test_copy_directory_without_overwrite", "test/test_directory_operations.py::TestDirectoryOperations::test_create_directory_already_exists", "test/test_directory_operations.py::TestDirectoryOperations::test_create_directory_success", "test/test_directory_operations.py::TestDirectoryOperations::test_create_directory_with_parents", "test/test_directory_operations.py::TestDirectoryOperations::test_delete_directory_not_empty_without_recursive", "test/test_directory_operations.py::TestDirectoryOperations::test_delete_directory_recursive", "test/test_directory_operations.py::TestDirectoryOperations::test_delete_directory_success", "test/test_directory_operations.py::TestDirectoryOperations::test_delete_nonexistent_directory", "test/test_directory_operations.py::TestDirectoryOperations::test_get_directory_info_nonexistent", "test/test_directory_operations.py::TestDirectoryOperations::test_get_directory_info_success", "test/test_directory_operations.py::TestDirectoryOperations::test_get_operation_stats", "test/test_directory_operations.py::TestDirectoryOperations::test_list_directory_tree", "test/test_directory_operations.py::TestDirectoryOperations::test_move_directory_success", "test/test_directory_operations.py::TestDirectoryOperations::test_reset_stats", "test/test_document_processing_integration.py::TestDocumentProcessingIntegration::test_batch_document_processing_workflow", "test/test_document_processing_integration.py::TestDocumentProcessingIntegration::test_config_processor_format_converter_integration", "test/test_document_processing_integration.py::TestDocumentProcessingIntegration::test_document_processor_text_analyzer_integration", "test/test_document_processing_integration.py::TestDocumentProcessingIntegration::test_document_search_and_analysis", "test/test_document_processing_integration.py::TestDocumentProcessingIntegration::test_end_to_end_document_workflow", "test/test_document_processing_integration.py::TestDocumentProcessingIntegration::test_error_handling_integration", "test/test_document_processing_integration.py::TestDocumentProcessingIntegration::test_format_conversion_chain", "test/test_document_processing_integration.py::TestDocumentProcessingIntegration::test_statistics_integration", "test/test_document_processor.py::TestDocumentProcessor::test_batch_process_documents", "test/test_document_processor.py::TestDocumentProcessor::test_concurrent_processing", "test/test_document_processor.py::TestDocumentProcessor::test_document_metadata_extraction", "test/test_document_processor.py::TestDocumentProcessor::test_format_detection", "test/test_document_processor.py::TestDocumentProcessor::test_process_csv_document", "test/test_document_processor.py::TestDocumentProcessor::test_process_ini_document", "test/test_document_processor.py::TestDocumentProcessor::test_process_invalid_json", "test/test_document_processor.py::TestDocumentProcessor::test_process_json_document", "test/test_document_processor.py::TestDocumentProcessor::test_process_nonexistent_document", "test/test_document_processor.py::TestDocumentProcessor::test_process_text_document", "test/test_document_processor.py::TestDocumentProcessor::test_process_xml_document", "test/test_document_processor.py::TestDocumentProcessor::test_processing_stats", "test/test_document_processor.py::TestDocumentProcessor::test_search_case_sensitive", "test/test_document_processor.py::TestDocumentProcessor::test_search_in_documents", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_config_file_structure", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_file_security", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_large_file", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_markdown_file", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_nonexistent_file", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_python_file_structure", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_single_file_basic", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_single_file_content", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_unsafe_file", "test/test_file_analyzer.py::TestFileAnalyzer::test_batch_analyze_files", "test/test_file_analyzer.py::TestFileAnalyzer::test_compare_files", "test/test_file_analyzer.py::TestFileAnalyzer::test_comprehensive_analysis", "test/test_file_analyzer.py::TestFileAnalyzer::test_concurrent_analysis", "test/test_file_analyzer.py::TestFileAnalyzer::test_find_similar_files", "test/test_file_analyzer.py::TestFileAnalyzer::test_get_analysis_stats", "test/test_file_analyzer.py::TestFileAnalyzer::test_reset_stats", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_complete_file_workflow", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_concurrent_operations", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_error_recovery", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_file_type_restrictions", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_file_utils_integration", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_large_file_handling", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_operation_statistics", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_path_utils_integration", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_security_integration", "test/test_format_converter.py::TestFormatConverter::test_batch_conversion", "test/test_format_converter.py::TestFormatConverter::test_csv_to_json_conversion", "test/test_format_converter.py::TestFormatConverter::test_encoding_conversion", "test/test_format_converter.py::TestFormatConverter::test_error_handling", "test/test_format_converter.py::TestFormatConverter::test_format_detection", "test/test_format_converter.py::TestFormatConverter::test_json_to_yaml_conversion", "test/test_format_converter.py::TestFormatConverter::test_statistics_tracking", "test/test_format_converter.py::TestFormatConverter::test_supported_formats", "test/test_format_converter.py::TestFormatConverter::test_text_file_conversion", "test/test_format_converter.py::TestFormatConverter::test_validation_functionality", "test/test_format_converter.py::TestFormatConverter::test_xml_dict_conversion", "test/test_format_converter.py::TestFormatConverter::test_xml_to_json_conversion", "test/test_search_operations.py::TestSearchOperations::test_cache_expiration", "test/test_search_operations.py::TestSearchOperations::test_cache_key_generation", "test/test_search_operations.py::TestSearchOperations::test_cache_operations", "test/test_search_operations.py::TestSearchOperations::test_close", "test/test_search_operations.py::TestSearchOperations::test_initialization", "test/test_search_operations.py::TestSearchOperations::test_initialization_without_api_key", "test/test_search_operations.py::TestSearchOperations::test_search_stats", "test/test_search_operations.py::TestSearchOperations::test_search_web_with_cache", "test/test_search_operations.py::TestSearchOperations::test_search_web_with_mock", "test/test_search_operations.py::TestTavilySearchClient::test_client_initialization", "test/test_search_operations.py::TestTavilySearchClient::test_rate_limiting", "test/test_search_operations.py::TestTavilySearchClient::test_search_api_error", "test/test_search_operations.py::TestTavilySearchClient::test_search_success", "test/test_search_operations.py::TestTavilySearchClient::test_search_timeout", "test/test_system_integration.py::TestSystemIntegration::test_batch_task_execution", "test/test_system_integration.py::TestSystemIntegration::test_complex_workflow_execution", "test/test_system_integration.py::TestSystemIntegration::test_concurrent_task_execution", "test/test_system_integration.py::TestSystemIntegration::test_end_to_end_file_read_workflow", "test/test_system_integration.py::TestSystemIntegration::test_end_to_end_file_write_workflow", "test/test_system_integration.py::TestSystemIntegration::test_error_handling_and_recovery", "test/test_system_integration.py::TestSystemIntegration::test_performance_benchmarks", "test/test_system_integration.py::TestSystemIntegration::test_supported_operations_coverage", "test/test_task_integration.py::TestFileOperationTaskExecutor::test_file_read_task", "test/test_task_integration_simple.py::test_batch_operations", "test/test_task_integration_simple.py::test_create_file_operation_task", "test/test_task_integration_simple.py::test_file_operation_task_executor_basic", "test/test_task_integration_simple.py::test_task_system_integrator_basic", "test/test_text_analyzer.py::TestTextAnalyzer::test_analysis_options", "test/test_text_analyzer.py::TestTextAnalyzer::test_basic_text_analysis", "test/test_text_analyzer.py::TestTextAnalyzer::test_batch_analysis", "test/test_text_analyzer.py::TestTextAnalyzer::test_content_classification", "test/test_text_analyzer.py::TestTextAnalyzer::test_error_handling", "test/test_text_analyzer.py::TestTextAnalyzer::test_file_analysis", "test/test_text_analyzer.py::TestTextAnalyzer::test_language_detection", "test/test_text_analyzer.py::TestTextAnalyzer::test_pattern_extraction", "test/test_text_analyzer.py::TestTextAnalyzer::test_quality_assessment", "test/test_text_analyzer.py::TestTextAnalyzer::test_statistics_tracking", "test/test_text_analyzer.py::TestTextAnalyzer::test_structure_analysis", "test/test_text_analyzer.py::TestTextAnalyzer::test_word_frequency_analysis"]