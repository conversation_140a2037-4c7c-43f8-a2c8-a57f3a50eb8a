"""
HyAIAgent 第四阶段 - 内容处理器模块
负责搜索结果的解析、清理和结构化处理
"""

import re
import html
import json
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from urllib.parse import urlparse, urljoin
from loguru import logger

from operations.search_operations import SearchResult, SearchResponse
from core.config_manager import ConfigManager
from operations.security_manager import SecurityManager


@dataclass
class ProcessedContent:
    """处理后的内容数据模型"""
    original_url: str
    title: str
    cleaned_content: str
    summary: str
    keywords: List[str]
    entities: List[Dict[str, Any]]
    content_type: str
    language: str
    word_count: int
    reading_time: int  # 预估阅读时间（分钟）
    quality_score: float  # 内容质量评分
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class ProcessedResponse:
    """处理后的搜索响应数据模型"""
    original_query: str
    processed_results: List[ProcessedContent]
    total_processed: int
    processing_time: float
    summary: str
    key_insights: List[str]
    related_topics: List[str]
    confidence_score: float
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['processed_results'] = [result.to_dict() for result in self.processed_results]
        return data


class ContentProcessor:
    """内容处理器 - 负责搜索结果的解析和处理"""
    
    def __init__(self, 
                 config_manager: ConfigManager,
                 security_manager: Optional[SecurityManager] = None):
        """
        初始化内容处理器
        
        Args:
            config_manager: 配置管理器
            security_manager: 安全管理器（可选）
        """
        self.config_manager = config_manager
        self.security_manager = security_manager
        
        # 加载处理配置
        self.processor_config = self.config_manager.get_config().get("content_processor", {})
        
        # 初始化处理统计
        self.processing_stats = {
            "total_processed": 0,
            "successful_processed": 0,
            "failed_processed": 0,
            "total_processing_time": 0.0,
            "average_processing_time": 0.0,
            "quality_scores": []
        }
        
        # 编译正则表达式模式
        self._compile_patterns()
        
        logger.info("内容处理器初始化完成")
    
    def _compile_patterns(self):
        """编译常用的正则表达式模式"""
        self.patterns = {
            # HTML标签清理
            'html_tags': re.compile(r'<[^>]+>'),
            'html_entities': re.compile(r'&[a-zA-Z0-9#]+;'),
            
            # 文本清理
            'multiple_spaces': re.compile(r'\s+'),
            'multiple_newlines': re.compile(r'\n\s*\n'),
            'special_chars': re.compile(r'[^\w\s\u4e00-\u9fff.,!?;:()\-\'""]'),
            
            # 内容提取
            'sentences': re.compile(r'[.!?]+\s+'),
            'words': re.compile(r'\b\w+\b'),
            'chinese_words': re.compile(r'[\u4e00-\u9fff]+'),
            'english_words': re.compile(r'\b[a-zA-Z]+\b'),
            
            # 实体识别
            'urls': re.compile(r'https?://[^\s]+'),
            'emails': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            'dates': re.compile(r'\b\d{4}[-/]\d{1,2}[-/]\d{1,2}\b'),
            'numbers': re.compile(r'\b\d+(?:\.\d+)?\b'),
        }
    
    async def process_search_response(self, search_response: SearchResponse) -> ProcessedResponse:
        """
        处理搜索响应，返回结构化的处理结果
        
        Args:
            search_response: 原始搜索响应
            
        Returns:
            ProcessedResponse: 处理后的响应数据
        """
        start_time = datetime.now()
        
        try:
            logger.info(f"开始处理搜索响应: {search_response.query}")
            
            # 处理每个搜索结果
            processed_results = []
            for result in search_response.results:
                try:
                    processed_content = await self._process_single_result(result)
                    if processed_content:
                        processed_results.append(processed_content)
                        self.processing_stats["successful_processed"] += 1
                except Exception as e:
                    logger.warning(f"处理单个结果失败: {result.url} - {str(e)}")
                    self.processing_stats["failed_processed"] += 1
                    continue
            
            # 生成整体摘要和洞察
            summary = await self._generate_overall_summary(processed_results, search_response.query)
            key_insights = await self._extract_key_insights(processed_results)
            related_topics = await self._identify_related_topics(processed_results)
            confidence_score = self._calculate_confidence_score(processed_results)
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # 更新统计信息
            self.processing_stats["total_processed"] += 1
            self.processing_stats["total_processing_time"] += processing_time
            self.processing_stats["average_processing_time"] = (
                self.processing_stats["total_processing_time"] / 
                self.processing_stats["total_processed"]
            )
            
            # 创建处理后的响应
            processed_response = ProcessedResponse(
                original_query=search_response.query,
                processed_results=processed_results,
                total_processed=len(processed_results),
                processing_time=processing_time,
                summary=summary,
                key_insights=key_insights,
                related_topics=related_topics,
                confidence_score=confidence_score,
                timestamp=datetime.now()
            )
            
            logger.info(f"搜索响应处理完成: {len(processed_results)}个结果")
            return processed_response
            
        except Exception as e:
            logger.error(f"处理搜索响应失败: {str(e)}")
            self.processing_stats["failed_processed"] += 1
            raise
    
    async def _process_single_result(self, result: SearchResult) -> Optional[ProcessedContent]:
        """
        处理单个搜索结果
        
        Args:
            result: 单个搜索结果
            
        Returns:
            ProcessedContent: 处理后的内容，如果处理失败返回None
        """
        try:
            # 清理和标准化内容
            cleaned_content = self._clean_content(result.content)
            
            # 生成摘要
            summary = self._generate_summary(cleaned_content)
            
            # 提取关键词
            keywords = self._extract_keywords(cleaned_content)
            
            # 识别实体
            entities = self._extract_entities(cleaned_content)
            
            # 检测内容类型和语言
            content_type = self._detect_content_type(cleaned_content)
            language = self._detect_language(cleaned_content)
            
            # 计算统计信息
            word_count = self._count_words(cleaned_content)
            reading_time = self._estimate_reading_time(word_count)
            quality_score = self._calculate_quality_score(result, cleaned_content)
            
            # 记录质量评分用于统计
            self.processing_stats["quality_scores"].append(quality_score)
            
            return ProcessedContent(
                original_url=result.url,
                title=self._clean_title(result.title),
                cleaned_content=cleaned_content,
                summary=summary,
                keywords=keywords,
                entities=entities,
                content_type=content_type,
                language=language,
                word_count=word_count,
                reading_time=reading_time,
                quality_score=quality_score,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.warning(f"处理单个结果失败: {result.url} - {str(e)}")
            return None
    
    def _clean_content(self, content: str) -> str:
        """清理和标准化内容文本"""
        if not content:
            return ""
        
        # HTML解码
        cleaned = html.unescape(content)
        
        # 移除HTML标签
        cleaned = self.patterns['html_tags'].sub('', cleaned)
        
        # 移除HTML实体
        cleaned = self.patterns['html_entities'].sub('', cleaned)
        
        # 标准化空白字符
        cleaned = self.patterns['multiple_spaces'].sub(' ', cleaned)
        cleaned = self.patterns['multiple_newlines'].sub('\n\n', cleaned)
        
        # 移除特殊字符（保留基本标点）
        cleaned = self.patterns['special_chars'].sub('', cleaned)
        
        # 去除首尾空白
        cleaned = cleaned.strip()
        
        return cleaned
    
    def _clean_title(self, title: str) -> str:
        """清理标题文本"""
        if not title:
            return ""
        
        # 基本清理
        cleaned = html.unescape(title)
        cleaned = self.patterns['html_tags'].sub('', cleaned)
        cleaned = self.patterns['multiple_spaces'].sub(' ', cleaned)
        cleaned = cleaned.strip()
        
        return cleaned
    
    def _generate_summary(self, content: str, max_length: int = 200) -> str:
        """生成内容摘要"""
        if not content:
            return ""
        
        # 简单的摘要生成：取前几句话
        sentences = self.patterns['sentences'].split(content)
        summary = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and len(summary + sentence) <= max_length:
                summary += sentence + ". "
            else:
                break
        
        return summary.strip()
    
    def _extract_keywords(self, content: str, max_keywords: int = 10) -> List[str]:
        """提取关键词"""
        if not content:
            return []
        
        # 简单的关键词提取：基于词频
        words = self.patterns['words'].findall(content.lower())
        
        # 过滤停用词（简化版）
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'}
        
        # 统计词频
        word_freq = {}
        for word in words:
            if len(word) > 2 and word not in stop_words:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # 按频率排序并返回前N个
        keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in keywords[:max_keywords]]
    
    def _extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """提取实体信息"""
        entities = []
        
        # 提取URL
        urls = self.patterns['urls'].findall(content)
        for url in urls:
            entities.append({"type": "url", "value": url})
        
        # 提取邮箱
        emails = self.patterns['emails'].findall(content)
        for email in emails:
            entities.append({"type": "email", "value": email})
        
        # 提取日期
        dates = self.patterns['dates'].findall(content)
        for date in dates:
            entities.append({"type": "date", "value": date})
        
        # 提取数字
        numbers = self.patterns['numbers'].findall(content)
        for number in numbers[:5]:  # 限制数量
            entities.append({"type": "number", "value": number})
        
        return entities
    
    def _detect_content_type(self, content: str) -> str:
        """检测内容类型"""
        if not content:
            return "unknown"
        
        content_lower = content.lower()
        
        # 简单的内容类型检测
        if any(word in content_lower for word in ['新闻', 'news', '报道', '记者']):
            return "news"
        elif any(word in content_lower for word in ['博客', 'blog', '个人', '分享']):
            return "blog"
        elif any(word in content_lower for word in ['学术', 'research', '论文', '研究']):
            return "academic"
        elif any(word in content_lower for word in ['产品', 'product', '服务', '公司']):
            return "commercial"
        else:
            return "general"
    
    def _detect_language(self, content: str) -> str:
        """检测内容语言"""
        if not content:
            return "unknown"
        
        # 简单的语言检测
        chinese_chars = len(self.patterns['chinese_words'].findall(content))
        english_words = len(self.patterns['english_words'].findall(content))
        
        if chinese_chars > english_words:
            return "zh"
        elif english_words > 0:
            return "en"
        else:
            return "unknown"
    
    def _count_words(self, content: str) -> int:
        """统计词数"""
        if not content:
            return 0
        
        # 中英文混合词数统计
        chinese_chars = len(self.patterns['chinese_words'].findall(content))
        english_words = len(self.patterns['english_words'].findall(content))
        
        # 中文按字符计算，英文按单词计算
        return chinese_chars + english_words
    
    def _estimate_reading_time(self, word_count: int) -> int:
        """估算阅读时间（分钟）"""
        # 假设中文200字/分钟，英文250词/分钟
        reading_speed = self.processor_config.get("reading_speed", 200)
        return max(1, word_count // reading_speed)
    
    def _calculate_quality_score(self, result: SearchResult, cleaned_content: str) -> float:
        """计算内容质量评分"""
        score = 0.0
        
        # 基于搜索结果评分
        if hasattr(result, 'score') and result.score:
            score += result.score * 0.3
        
        # 基于内容长度
        content_length = len(cleaned_content)
        if content_length > 500:
            score += 0.3
        elif content_length > 200:
            score += 0.2
        elif content_length > 50:
            score += 0.1
        
        # 基于标题质量
        if result.title and len(result.title) > 10:
            score += 0.2
        
        # 基于URL质量
        if result.url:
            parsed_url = urlparse(result.url)
            if parsed_url.netloc:
                score += 0.2
        
        return min(1.0, score)
    
    async def _generate_overall_summary(self, processed_results: List[ProcessedContent], query: str) -> str:
        """生成整体摘要"""
        if not processed_results:
            return f"未找到关于'{query}'的相关信息。"
        
        # 简单的整体摘要生成
        total_results = len(processed_results)
        avg_quality = sum(result.quality_score for result in processed_results) / total_results
        
        summary = f"关于'{query}'的搜索找到了{total_results}个相关结果，平均质量评分为{avg_quality:.2f}。"
        
        # 添加主要内容类型
        content_types = {}
        for result in processed_results:
            content_types[result.content_type] = content_types.get(result.content_type, 0) + 1
        
        if content_types:
            main_type = max(content_types.items(), key=lambda x: x[1])
            summary += f" 主要内容类型为{main_type[0]}。"
        
        return summary
    
    async def _extract_key_insights(self, processed_results: List[ProcessedContent]) -> List[str]:
        """提取关键洞察"""
        insights = []
        
        if not processed_results:
            return insights
        
        # 统计关键词频率
        all_keywords = []
        for result in processed_results:
            all_keywords.extend(result.keywords)
        
        keyword_freq = {}
        for keyword in all_keywords:
            keyword_freq[keyword] = keyword_freq.get(keyword, 0) + 1
        
        # 生成基于关键词的洞察
        top_keywords = sorted(keyword_freq.items(), key=lambda x: x[1], reverse=True)[:5]
        if top_keywords:
            insights.append(f"最频繁提及的关键词：{', '.join([kw for kw, freq in top_keywords])}")
        
        # 质量分析洞察
        high_quality_count = sum(1 for result in processed_results if result.quality_score > 0.7)
        if high_quality_count > 0:
            insights.append(f"发现{high_quality_count}个高质量内容源")
        
        return insights
    
    async def _identify_related_topics(self, processed_results: List[ProcessedContent]) -> List[str]:
        """识别相关主题"""
        if not processed_results:
            return []
        
        # 基于关键词聚类识别相关主题
        all_keywords = []
        for result in processed_results:
            all_keywords.extend(result.keywords)
        
        # 简单的主题识别：返回高频关键词作为相关主题
        keyword_freq = {}
        for keyword in all_keywords:
            keyword_freq[keyword] = keyword_freq.get(keyword, 0) + 1
        
        # 过滤低频词并返回
        related_topics = [kw for kw, freq in keyword_freq.items() if freq >= 2]
        return related_topics[:10]  # 限制数量
    
    def _calculate_confidence_score(self, processed_results: List[ProcessedContent]) -> float:
        """计算整体置信度评分"""
        if not processed_results:
            return 0.0
        
        # 基于结果数量和质量计算置信度
        result_count_score = min(1.0, len(processed_results) / 5.0)  # 5个结果为满分
        avg_quality_score = sum(result.quality_score for result in processed_results) / len(processed_results)
        
        confidence = (result_count_score * 0.4 + avg_quality_score * 0.6)
        return round(confidence, 2)
    
    async def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        stats = self.processing_stats.copy()
        
        # 计算成功率
        total = stats["total_processed"]
        if total > 0:
            stats["success_rate"] = stats["successful_processed"] / total
        else:
            stats["success_rate"] = 0.0
        
        # 计算平均质量评分
        if stats["quality_scores"]:
            stats["average_quality_score"] = sum(stats["quality_scores"]) / len(stats["quality_scores"])
        else:
            stats["average_quality_score"] = 0.0
        
        return stats
