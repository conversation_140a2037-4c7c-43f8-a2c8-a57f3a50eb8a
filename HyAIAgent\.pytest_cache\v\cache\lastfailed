{"test/test_text_analyzer.py::TestTextAnalyzer::test_file_analysis": true, "test/test_text_analyzer.py::TestTextAnalyzer::test_batch_analysis": true, "test/test_document_processor.py::TestDocumentProcessor::test_process_text_document": true, "test/test_document_processor.py::TestDocumentProcessor::test_process_json_document": true, "test/test_document_processor.py::TestDocumentProcessor::test_process_csv_document": true, "test/test_document_processor.py::TestDocumentProcessor::test_process_xml_document": true, "test/test_document_processor.py::TestDocumentProcessor::test_process_ini_document": true, "test/test_document_processor.py::TestDocumentProcessor::test_batch_process_documents": true, "test/test_document_processor.py::TestDocumentProcessor::test_search_in_documents": true, "test/test_document_processor.py::TestDocumentProcessor::test_process_invalid_json": true, "test/test_document_processor.py::TestDocumentProcessor::test_document_metadata_extraction": true, "test/test_document_processor.py::TestDocumentProcessor::test_processing_stats": true, "test/test_document_processor.py::TestDocumentProcessor::test_concurrent_processing": true, "test/test_task_integration.py": true, "test/test_task_integration.py::TestFileOperationTaskExecutor::test_file_read_task": true, "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_single_file_basic": true, "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_single_file_content": true, "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_python_file_structure": true, "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_config_file_structure": true, "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_file_security": true, "test/test_file_analyzer.py::TestFileAnalyzer::test_comprehensive_analysis": true, "test/test_file_analyzer.py::TestFileAnalyzer::test_batch_analyze_files": true, "test/test_file_analyzer.py::TestFileAnalyzer::test_compare_files": true, "test/test_file_analyzer.py::TestFileAnalyzer::test_find_similar_files": true, "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_nonexistent_file": true, "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_unsafe_file": true, "test/test_file_analyzer.py::TestFileAnalyzer::test_get_analysis_stats": true, "test/test_file_analyzer.py::TestFileAnalyzer::test_reset_stats": true, "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_large_file": true, "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_markdown_file": true, "test/test_file_analyzer.py::TestFileAnalyzer::test_concurrent_analysis": true}